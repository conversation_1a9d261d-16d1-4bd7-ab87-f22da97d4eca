[project]
name = "hw-2505"
version = "0.1.0"
description = "Add your description here"
readme = "README.md"
requires-python = ">=3.12"
dependencies = [
    "aiomysql>=0.2.0",
    "alibabacloud-dm20151123==1.2.4",
    "click>=8.2.1",
    "email-validator>=2.2.0",
    "fastapi>=0.115.12",
    "gunicorn>=23.0.0",
    "jinja2>=3.1.6",
    "loguru>=0.7.3",
    "orjson>=3.10.18",
    "pydantic>=2.11.5",
    "redis>=6.2.0",
    "ruff>=0.11.11",
    "tortoise-orm>=0.25.0",
    "ujson>=5.10.0",
    "uvicorn>=0.34.2",
]


[[tool.uv.index]]
name = "ali"
url = "https://mirrors.aliyun.com/pypi/simple/"
default = true

[[tool.uv.index]]
name = "tsinghua"
url = "https://pypi.tuna.tsinghua.edu.cn/simple"
explicit = true

[dependency-groups]
dev = [
    "ruff>=0.11.11",
]
