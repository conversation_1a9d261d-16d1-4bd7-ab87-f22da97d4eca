version: "3"

services:
  redis:
    image: "redis"
    container_name: redis
    command: redis-server --requirepass 654321
    ports:
      - "6379:6379"
    networks:
      root_network:
        ipv4_address: **********

  mysql:
    image: "mysql"
    container_name: "mysql"
    ports:
      - "3306:3306"
    environment:
      MYSQL_ROOT_PASSWORD: 654321
    volumes:
      - "~/.data/mysql-data/conf/:/etc/my.cnf.d"
      - "~/.data/mysql-data/mysql-files:/var/lib/mysql-files"
      - "~/.data/mysql-data/mysql:/var/lib/mysql"
    networks:
      root_network:
        ipv4_address: **********

networks:
  root_network:
    ipam:
      config:
        - subnet: **********/16
          gateway: **********
