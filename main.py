import logging
import traceback
from contextlib import asynccontextmanager

import uvicorn
from fastapi import <PERSON><PERSON><PERSON>
from fastapi import <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>
from fastapi import Request
from fastapi.exception_handlers import RequestValidationError
from fastapi.middleware.cors import CORSMiddleware
from tortoise import Tortoise
from tortoise.contrib.fastapi import register_tortoise

from app.common.logger import logger
from app.common.resp_json import ApiJsonify
from app.config.settings import Settings

logging.basicConfig(level=logging.INFO)
logging.getLogger('tortoise').setLevel(logging.INFO)


@asynccontextmanager
async def lifespan(app: FastAPI):
    from app.model.base import get_config
    from app.common.redis_store import redis_store
    await Tortoise.init(config=get_config())
    await redis_store.open()
    yield

    await Tortoise.close_connections()
    await redis_store.aclose(close_connection_pool=True)


def create_app():
    app = FastAPI(
        debug=Settings.DEBUG,
        title=Settings.TITLE,
        docs_url=Settings.DOCS_URL,
        redoc_url=Settings.REDOCS_URL,
        openapi_url=Settings.OPENAPI_URL,
        lifespan=lifespan,
    )
    configuration_routers(app)
    configuration_middleware(app)
    configuration_app(app)
    configuration_db(app)
    return app


def configuration_routers(app: FastAPI):
    from app.api.admin import router as admin_router
    from app.api.client import router as client_router

    app.include_router(admin_router)
    app.include_router(client_router)
    app.include_router(client_router.default_router)


def configuration_db(app: FastAPI):
    from app.model.base import get_config as mysql_config
    register_tortoise(
        app=app,
        config=mysql_config(),
    )


def configuration_middleware(app: FastAPI):
    app.add_middleware(
        CORSMiddleware,
        allow_origins=[
            '*',
        ],
        allow_credentials=True,
        allow_methods=['GET', 'POST', 'PUT', 'OPTIONS', 'DELETE'],
        allow_headers=['Content-Type', 'Authorization', 'Token', 'X-Token'],
    )

    @app.middleware('http')
    async def unhandled_exception_handler(request: Request, call_next):
        try:
            return await call_next(request)
        except Exception:
            traceback_errmsg = traceback.format_exc()
            logger.error(f"unhandled_exception_handler: {traceback_errmsg}")
            return ApiJsonify.no('Internal Server Errorr', code=500)


def configuration_app(app: FastAPI):
    from app.common.errors import BaseError

    def get_err_message(value_error):
        err_types = {
            'value_error.missing': '`{err_field}` 不能为空',
        }
        err = value_error[0]
        err_field = err['loc'][1] if len(err['loc']) > 1 else err['loc'][0]
        err_type = err_types.get(err['type'])
        if err_type:
            return err_type.format(err_field=err_field)
        else:
            return f'无效的参数 {err_field}'

    @app.exception_handler(RequestValidationError)
    async def validation_exception_handler(request: Request, exc):
        errmsg = get_err_message(exc.errors())
        return ApiJsonify.no(errmsg, code=400)

    @app.exception_handler(404)
    async def not_found_exception_handler(request, exc):
        return ApiJsonify.no('Page not found', code=exc.status_code)

    @app.exception_handler(HTTPException)
    async def http_exception_handler(request, exc: HTTPException):
        logger.error(f'http_exception_handler: {exc.detail}')
        return ApiJsonify.no(exc.detail, code=exc.status_code)

    @app.exception_handler(BaseError)
    async def server_exception_handler(request, exc: BaseError):
        traceback_errmsg = traceback.format_exc()
        logger.error(f'server_exception_handler: {traceback_errmsg}')
        return ApiJsonify.no(exc.message, code=exc.code)


app = create_app()

if __name__ == '__main__':
    uvicorn.run('main:app', host='0.0.0.0', port=5000, reload=True)
