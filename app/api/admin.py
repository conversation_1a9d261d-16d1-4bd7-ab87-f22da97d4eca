from app.common.route import create_router
from app.model.user import Meeting, Image, Toupiao, Option, User
from app.common.auth import Token<PERSON>uth
from app.api import schema
from app.common.resp_json import ApiJsonify as resp
from fastapi import Depends
import orjson as json
from datetime import datetime
from app.common.redis_store import redis_store

router = create_router(tags=["管理端"])


@router.post('/version', summary="设置版本号")
async def set_version(form: schema.VerionSchema,  token: str = Depends(TokenAuth.admin_auth)):
    """
    设置版本号
    """
    meeting = await Meeting.filter(id=1).first()
    if meeting:
        meeting.version = form.version
        await meeting.save()
        await Meeting.save_to_cache(meeting)
    else:
        meeting = Meeting(id=1, version=form.version)
        await meeting.save()
    return resp.ok()


@router.post('/imgdata', summary="更新图片数据")
async def update_img_data(form: schema.UpdateImgDataSchema,  token: str = Depends(TokenAuth.admin_auth)):
    """
    更新图片数据
    """
    url_list = json.dumps(form.url_list)
    img = await Image.filter(type=form.type).first()
    if img:
        img.url_list = url_list
        img.need_update = True
        await img.save()
        await Image.save_to_cache(img)
    else:
        img = Image(type=form.type, url_list=url_list, need_update=True)
        await img.save()
    return resp.ok()


@router.post('/username/limit', summary='设置上传姓名数量限制')
async def set_username_limit(form: schema.UsernameLimitSchema, token: str = Depends(TokenAuth.admin_auth)):
    meeting = await Meeting.filter(id=1).first()
    if meeting:
        meeting.username_limit = form.user_count
        await meeting.save()
        await Meeting.save_to_cache(meeting)
    else:
        meeting = Meeting(id=1, username_limit=form.user_count)
        await meeting.save()

    return resp.ok()


@router.post('/fayan', summary='设置发言状态')
async def set_fayan_status(form: schema.FayanSchema, token: str = Depends(TokenAuth.admin_auth)):
    meeting = await Meeting.filter(id=1).first()
    if meeting:
        meeting.fayan = form.status
        await meeting.save()
        await Meeting.save_to_cache(meeting)
    else:
        meeting = Meeting(id=1, fayan=form.status)
        await meeting.save()

    if not form.status:
        await User.filter().update(deleted=True, delete_time=datetime.now())
    return resp.ok()


@router.post('/screen', summary='设置切换直播画面')
async def set_screen(form: schema.FayanSchema, token: str = Depends(TokenAuth.admin_auth)):
    meeting = await Meeting.filter(id=1).first()
    if meeting:
        meeting.screen = form.status
        await meeting.save()
        await Meeting.save_to_cache(meeting)
    else:
        meeting = Meeting(id=1, screen=form.status)
        await meeting.save()

    return resp.ok()


@router.post('/debug/memo', summary='设置笔记状态')
async def set_debug_memo(form: schema.FayanSchema, token: str = Depends(TokenAuth.admin_auth)):
    meeting = await Meeting.filter(id=1).first()
    if meeting:
        meeting.memo = form.status
        await meeting.save()
        await Meeting.save_to_cache(meeting)
    else:
        meeting = Meeting(id=1, memo=form.status)
        await meeting.save()
    return resp.ok()


@router.post('/toupiao', summary='设置投票开关和数据')
async def set_toupiao(form: schema.ToupiaoSchema, token: str = Depends(TokenAuth.admin_auth)):
    toupiao = await Toupiao.filter(id=1).first()

    option1_list = json.dumps(form.option1_list)
    option2_list = json.dumps(form.option1_list)

    if toupiao:
        toupiao.status = form.status
        toupiao.duoxuan = form.duoxuan
        toupiao.title1 = form.title1
        toupiao.title2 = form.title2
        toupiao.option1_list = option1_list
        toupiao.option2_list = option2_list
        await toupiao.save()
        await Toupiao.save_to_cache(toupiao)
    else:
        toupiao = Toupiao(id=1, status=form.status, duoxuan=form.duoxuan, title1=form.title1, title2=form.title2,
                          option1_list=option1_list, option2_list=option2_list)
        await toupiao.save()

    await Option.filter().update(deleted=True, delete_time=datetime.now())
    await redis_store.delete('options')
    return resp.ok()
