from app.common.route import create_router
from app.model.user import Meeting, Image, Toupiao, Option, User, Email, Username
from app.common.auth import TokenAuth
from app.api import schema
from app.common.resp_json import ApiJsonify as resp
from fastapi import Depends, Query
from app.common.errors import BizError
from app.common.logger import logger
import orjson as json
from app.api.mail import MailService
from datetime import datetime, timedelta
from app.common.redis_store import redis_store
import time

router = create_router(tags=["客户端-180台"])


def get_ms(t1: float):
    t1 = int(t1 * 1000)
    return int(time.time() * 1000) - t1


async def get_meeting():
    t1 = time.time()
    cache_meeting = await Meeting.get_from_cache(1)
    if cache_meeting:
        logger.info(f"从缓存中获取会议信息 耗时: {get_ms(t1)}ms")
        return cache_meeting
    
    meeting =  await Meeting.filter().first()
    if not meeting:
        meeting = Meeting(id=1)
        await meeting.save()

    await Meeting.save_to_cache(meeting)
    logger.info(f"从数据库中获取会议信息 耗时: {get_ms(t1)}ms")
    return meeting


async def get_toupiao_data():
    t1 = time.time()
    cache_toupiao = await Toupiao.get_from_cache(1)
    if cache_toupiao:
        logger.info(f"从缓存中获取投票信息 耗时: {get_ms(t1)}ms")
        return cache_toupiao
    
    toupiao = await Toupiao.filter().first()
    await Toupiao.save_to_cache(toupiao)
    logger.info(f"从数据库中获取投票信息 耗时: {get_ms(t1)}ms")
    return toupiao

async def get_image_data(image_type: str):
    """ 获取缓存图片数据"""
    t1 = time.time()
    cache_image = await Image.get_from_cache(image_type)
    if cache_image:
        logger.info(f"从缓存中获取图片信息 耗时: {get_ms(t1)}ms")
        return cache_image
    
    image = await Image.filter(type=image_type).first()
    if not image:
        image = Image(type=image_type, url_list='', need_update=False)
        await image.save()
    await Image.save_to_cache(image)
    logger.info(f"从数据库中获取图片信息 耗时: {get_ms(t1)}ms")
    return image

async def get_user_list_data():
    """ 获取用户名列表 """
    t1 = time.time()
    cache_usernames = await redis_store.lrange('usernames', 0, -1)
    if cache_usernames:
        logger.info(f"从缓存中获取用户名列表 耗时:  {get_ms(t1)}ms")
        cache_usernames = list(set(cache_usernames))
        return cache_usernames
    
    # logger.info("从数据库中获取用户名列表")
    # usernames = await User.filter().values_list('name', flat=True)
    # if not usernames:
    #     return []
    # await redis_store.lpush('usernames', *usernames)
    # return usernames
    return []


async def get_option_data():
    """ 获取选项缓存"""
    t1 = time.time()
    cache_options = await redis_store.lrange('options', 0, -1)
    if cache_options:
        logger.info(f"从缓存中获取选项列表 耗时:  {get_ms(t1)}ms")
        cache_options = [json.loads(option) for option in cache_options]
        return cache_options

    # logger.info("从数据库中获取选项列表")
    # options = await Option.filter().all()
    # options = [option.json() for option in options]
    # if not options:
    #     return []
    # await redis_store.lpush('options', *[json.dumps(option) for option in options])
    # return options
    return []


@router.post('/send_email', summary='发送邮件')
async def send_email(form: schema.SendEmailSchema,  token: str = Depends(TokenAuth.client_auth)):
    """
    发送邮件
    """
    email = await Email.filter(email=form.email).order_by('-id').first()
    if email and email.create_time + timedelta(minutes=1) > datetime.now():
        return resp.no(message='发送邮件过于频繁，请稍后再试')
    
    status = MailService.send_mail(
        address=[form.email],
        imgs=form.img_list
    )
    if status:
        await Email.create(email=form.email, img_list=json.dumps(form.img_list))
    return resp.ok()


@router.get('/imgdata', summary='获取图片数据')
async def get_img_data(type: str = Query(..., description="图片类型", examples=["HUIYI", "JIABIN", 'ZHIBO']), token: str = Depends(TokenAuth.client_auth)):
    """
    获取图片数据
    """
    img = await get_image_data(type)
    if img:
        need_update = img.need_update
        if need_update:
            img.need_update = False
            await Image.save_to_cache(img)
        return resp.ok(url_list=json.loads(img.url_list or '[]'), need_update=need_update)
    return resp.ok(url_list=[], need_update=False)


@router.default_router.get('/version', summary="获取版本信息")
async def get_version(token: str = Depends(TokenAuth.client_auth)):
    """
    获取版本信息
    """
    meeting = await get_meeting()
    return resp.ok(version=meeting.version)


@router.post('/username', summary="上传名称")
async def upload_username(form: schema.UploadUsernameSchema, token: str = Depends(TokenAuth.client_auth)):
    """
    上传名称
    """
    meeting = await get_meeting()
    user = await User.filter(name=form.name).first()
    if user:
        logger.info(f"上传名称: {form.name} 已存在")
        return resp.ok(status=False)
    
    user_count = await User.filter().count()
    if user_count >= meeting.username_limit:
        logger.info(f"上传名称失败，已达到限制，当前数量：{user_count}")
        return resp.no(message='上传名称已达到限制')

    await User.create(name=form.name)
    await redis_store.lpush('usernames', form.name)
    return resp.ok()

@router.post('/unique_name', summary="上传名称校验")
async  def check_username(form: schema.UploadUsernameSchema, token: str = Depends(TokenAuth.client_auth)):
    """
    上传名称校验
    """
    user = await Username.filter(name=form.name).first()
    if user:
        return resp.no(message='上传名称已存在', status=False)
    await Username.create(name=form.name)
    return resp.ok()


@router.get('/username', summary='获取上传名称列表')
async def get_username_list(token: str = Depends(TokenAuth.client_auth)):
    """
    获取上传名称列表
    """
    user_list = await get_user_list_data()
    return resp.ok(user_list=user_list)


@router.default_router.get('/fayan', summary='获取发言状态')
async def get_fayan_status(token: str = Depends(TokenAuth.client_auth)):
    """
    获取发言状态
    """
    meeting = await get_meeting()
    if meeting:
        return resp.ok(status=meeting.fayan)
    return resp.ok(status=False)

@router.default_router.get('/screen', summary='获取切换直播画面状态')
async def get_screen_status(token: str = Depends(TokenAuth.client_auth)):
    """
    获取切换直播画面状态
    """
    meeting = await get_meeting()
    if meeting:
        return resp.ok(status=meeting.screen)
    return resp.ok(status=False)


@router.get('/unsubscribe', summary='退订邮件')
async def unsubscribe():
    """
    退订邮件
    """
    return "Unsubscribe successfully."


@router.default_router.get('/debug/memo', summary='获取笔记状态')
async def get_debug_memo(token: str = Depends(TokenAuth.client_auth)):
    """
    获取笔记状态
    """
    meeting = await get_meeting()
    if meeting:
        return resp.ok(status=meeting.memo)
    return resp.ok(status=False)


@router.default_router.get('/toupiao', summary='获取投票开关和数据')
async def get_toupiao(token: str = Depends(TokenAuth.client_auth)):
    """
    获取投票状态
    """
    toupiao = await get_toupiao_data()
    if toupiao:
        return resp.ok(**toupiao.json())
    return resp.ok()


@router.post('/toupiao/option', summary='投票接口')
async def toupiao_option(form: schema.ToupiaoOptionSchema, token: str = Depends(TokenAuth.client_auth)):
    """
    投票接口
    """
    option1 = json.dumps(form.option1)
    option2 = json.dumps(form.option2)
    op = await Option.create(option1=option1, option2=option2)
    await redis_store.lpush('options', json.dumps(op.json()))
    return resp.ok()


async def get_toupiao_summary_data():
    t1 = time.time()
    toupiao = await get_toupiao_data()
    if not toupiao:
        return {
            'option1': [],
            'option2': []
        }
    option1_list = json.loads(toupiao.option1_list)
    option2_list = json.loads(toupiao.option2_list)


    len_option1 = len(option1_list)
    len_option2 = len(option2_list)
    # 投票结果统计
    option1 = [0] * len_option1
    option2 = [0] * len_option2
    # 计算结果
    options = await get_option_data()
    for option in options:
        try:
            op1 = json.loads(option['option1'])
            op2 = json.loads(option['option2'])
        except Exception:
            continue
        for i in op1:
            if i > 0 and len_option1 >= i-1:
                option1[i-1] += 1

        for i in op2:
            if i > 0 and len_option2 >= i-1:
                option2[i-1] += 1

    
    logger.info(f'处理投票结果耗时：{get_ms(t1)}ms')
    return {
        'option1': option1,
        'option2': option2
    }


def get_toupiao_summary_data2(toupiao, options):
    t1 = time.time()
    if not toupiao:
        return {
            'option1': [],
            'option2': []
        }
    option1_list = json.loads(toupiao.option1_list)
    option2_list = json.loads(toupiao.option2_list)


    len_option1 = len(option1_list)
    len_option2 = len(option2_list)
    # 投票结果统计
    option1 = [0] * len_option1
    option2 = [0] * len_option2

    # 计算结果
    for option in options:
        try:
            op1 = json.loads(option['option1'])
            op2 = json.loads(option['option2'])
        except Exception:
            continue
        for i in op1:
            if i > 0 and len_option1 >= i-1:
                option1[i-1] += 1

        for i in op2:
            if i > 0 and len_option2 >= i-1:
                option2[i-1] += 1

    
    logger.info(f'处理投票结果耗时：{get_ms(t1)}ms')
    return {
        'option1': option1,
        'option2': option2
    }


@router.get('/toupiao/summary', summary='获取投票结果')
async def get_toupiao_summary(token: str = Depends(TokenAuth.client_auth)):
    """
    获取投票结果
    """
    res = await get_toupiao_summary_data()
    return resp.ok(**res)


@router.default_router.get('/summary', summary='获取统计信息')
async def summary(token: str = Depends(TokenAuth.client_auth)):
    """ 
    获取统计信息
    """
    meeting = await get_meeting()
    toupiao = await get_toupiao_data()
    user_list = await get_user_list_data()
    toupiao_summary = await get_toupiao_summary_data()

    # 图片数据相关
    t1 = time.time()
    res = {
        'fayan': meeting.fayan,
        'version': meeting.version,
        'memo': meeting.memo,
        'screen': meeting.screen,
        'toupiao': toupiao.json(),
        'user_list': user_list,
        'toupiao_summary': toupiao_summary,
        'image': {
        },
    }
    image_types = ["HUIYI", "JIABIN", "ZHIBO"]
    for img_type in image_types:
        img = await get_image_data(img_type)
        if img:
            need_update = img.need_update
            if need_update:
                img.need_update = False
                await Image.save_to_cache(img)
            res['image'][img_type] = {
                'url_list': json.loads(img.url_list or '[]'),
                'need_update': need_update
            }
        else:
            res['image'][img_type] = {
                'url_list': [],
                'need_update': False,
            }

    logger.info(f"计算图片数据成功 耗时: {get_ms(t1)}ms")
    return resp.ok(**res)


@router.default_router.get('/summary2', summary='获取统计信息2')
async def summary(token: str = Depends(TokenAuth.client_auth)):
    """ 
    获取统计信息
    """

    # 图片数据相关
    t1 = time.time()
    async with redis_store.pipeline() as pipe:
        pipe.get('meeting:1')
        pipe.get('toupiao:1')
        pipe.lrange('usernames', 0, -1)
        pipe.lrange('options', 0, -1)

        pipe.get('image:HUIYI')
        pipe.get('image:JIABIN')
        pipe.get('image:ZHIBO')
        result = await pipe.execute()

    meeting = Meeting.from_dict(Meeting.load_ensure_dict(result[0]))
    toupiao = Toupiao.from_dict(Toupiao.load_ensure_dict(result[1]))
    user_list = list(set(result[2]))
    options = [json.loads(i) for  i in result[3]]
    toupiao_summary = get_toupiao_summary_data2(toupiao, options)

    image_huiyi = Image.from_dict(Image.load_ensure_dict(result[4]))
    image_jiabin = Image.from_dict(Image.load_ensure_dict(result[5]))
    image_zhibo = Image.from_dict(Image.load_ensure_dict(result[6]))

    res = {
        'fayan': meeting.fayan,
        'version': meeting.version,
        'memo': meeting.memo,
        'screen': meeting.screen,
        'toupiao': toupiao.json(),
        'user_list': user_list,
        'toupiao_summary': toupiao_summary,
        'image': {
        },
    }

    image_types = ["HUIYI", "JIABIN", "ZHIBO"]
    image_dict = {
        'HUIYI': image_huiyi,
        'JIABIN': image_jiabin,
        'ZHIBO': image_zhibo,
    }
    for img_type in image_types:
        img = image_dict[img_type]
        if img:
            need_update = img.need_update
            if need_update:
                img.need_update = False
                await Image.save_to_cache(img)
            res['image'][img_type] = {
                'url_list': json.loads(img.url_list or '[]'),
                'need_update': need_update
            }
        else:
            res['image'][img_type] = {
                'url_list': [],
                'need_update': False,
            }

    logger.info(f"计算图片数据成功 耗时: {get_ms(t1)}ms")
    return resp.ok(**res)



@router.get('/load_cache')
async def load_cache(token: str = Depends(TokenAuth.client_auth)):
    meeting = await get_meeting()
    toupiao = await get_toupiao_data()
    return resp.ok()