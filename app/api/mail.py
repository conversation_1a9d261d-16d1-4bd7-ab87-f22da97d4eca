import os
import sys

from typing import List
from pathlib import Path

from alibabacloud_dm20151123.client import Client as Dm20151123Client
from alibabacloud_credentials.client import Client as CredentialClient
from alibabacloud_tea_openapi import models as open_api_models
from alibabacloud_dm20151123 import models as dm_20151123_models
from alibabacloud_tea_util import models as util_models
from alibabacloud_tea_util.client import Client as UtilClient
import jinja2
from app.config.settings import Settings
from app.common.logger import logger
from app.common.errors import BizError



class MailService:
    @staticmethod
    def get_html_content(items : List[str]):
        """ 渲染 HTML文件"""
        template_path = Path(Settings.ROOT_PATH) / 'app/templates'
        template_loader = jinja2.FileSystemLoader(searchpath=template_path)
        environment = jinja2.Environment(loader=template_loader)
        content = environment.get_template('mail.html').render(imgs=items)
        return content

    @staticmethod
    def create_client() -> Dm20151123Client:
        """
        使用凭据初始化账号Client
        @return: Client
        @throws Exception
        """
        # 工程代码建议使用更安全的无AK方式，凭据配置方式请参见：https://help.aliyun.com/document_detail/378659.html。
        credential = CredentialClient()
        config = open_api_models.Config(
            access_key_id='LTAI5tHYSEEAQYpwqf6QKRPg',
            access_key_secret='******************************'
        )
        # Endpoint 请参考 https://api.aliyun.com/product/Dm
        config.endpoint = f'dm.aliyuncs.com'
        return Dm20151123Client(config)

    @staticmethod
    def send_mail(
        address: List[str],
        imgs : List[str],
    ) -> None:
        client = MailService.create_client()
        htmlStr = MailService.get_html_content(imgs)

        single_send_mail_request = dm_20151123_models.SingleSendMailRequest(
            # html_body 改成从html文件获取内容
            html_body=htmlStr,
            account_name='<EMAIL>',
            address_type=1,
            reply_to_address=False,
            to_address=','.join(address), #<EMAIL>, <EMAIL>
            subject='MBBF Top Talk Summit Notes'
        )
        runtime = util_models.RuntimeOptions()
        try:
            # 复制代码运行请自行打印 API 的返回值
            res = client.single_send_mail_with_options(single_send_mail_request, runtime)
            logger.info(f"发送成功，address:{address}, res:{res}")
            return True
        except Exception as error:
            logger.error(f"发送失败，address:{address}, error:{error}")
            return False
