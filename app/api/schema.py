from pydantic import BaseModel, Field, field_validator, EmailStr
from typing import List
from app.common.errors import ClientError



class VerionSchema(BaseModel):
    version: str = Field(..., description="版本号", examples=["0.0.1"])


class UpdateImgDataSchema(BaseModel):
    type: str = Field(..., description="图片类型", examples=["HUIYI", "JIABIN", "ZHIBO"])
    url_list: List[str] = Field(..., description="图片地址列表", examples=["https://www.baidu.com/1.png,https://www.baidu.com/2.png"])

    @field_validator('type')
    @classmethod
    def validate_type(cls, v: str) -> str:
        if v not in ["HUIYI", "JIABIN", "ZHIBO"]:
            raise ClientError(message="图片类型错误")
        return v.upper()
    

class UsernameLimitSchema(BaseModel):
    user_count: int = Field(..., description="名称数量", examples=[10])


class FayanSchema(BaseModel):
    status: bool = Field(..., description="是否开启", examples=[True])


class ToupiaoSchema(BaseModel):
    status: bool = Field(..., description="是否开启", examples=[True])
    duoxuan: bool = Field(..., description="是否多选", examples=[True])
    title1: str = Field(..., description="标题1", examples=["标题1"])
    title2: str = Field(..., description="标题2", examples=["标题2"])
    option1_list: List[str] = Field(..., description="选项1列表", examples=[["选项1", "选项2"]])
    option2_list: List[str] = Field(..., description="选项2列表", examples=[["选项1", "选项2"]])



class SendEmailSchema(BaseModel):
    email: EmailStr = Field(..., description="邮箱", examples=["<EMAIL>"])
    img_list: List[str] = Field(..., description="图片列表", examples=[["图片1", "图片2"]])



class UploadUsernameSchema(BaseModel):
    name: str = Field(..., description="用户名", examples=["张三"])



class ToupiaoOptionSchema(BaseModel):
    option1: List[int] = Field(..., description="选项1", examples=[0])
    option2: List[int] = Field(..., description="选项2", examples=[0])
