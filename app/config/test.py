from pathlib import Path, PurePath

from .base import TEST, BaseConfig


class TestConfig(BaseConfig):
    SERVER_ENV = TEST

    # 日志配置
    LOG_CONFIG = {
        'LOG_DIR': PurePath.joinpath(Path(__file__).absolute().parent.parent.parent, 'log'),
        'APP_LOGGER': {
            'ALI_LOG_PROJECT': '',
            'ALI_LOG_STORE': '',
            'ALI_LOG_ERROR_STORE': '',
            'ALI_LOG_ENDPOINT': 'cn-hangzhou.log.aliyuncs.com',
            'ALI_LOG_ACCESS_KEY_ID': '',
            'ALI_LOG_ACCESS_KEY_SECRET': '',
        },
    }

    # DB
    DB_CONFIG = {
        'user': 'root',
        'host': '**********',
        'password': '654321',
        'port': 3306,
        'database': 'hw-2505',
    }

    # REDIS
    REDIS_CONFIG = {
        'host': '**********',
        'port': 6379,
        'username': 'default',
        'password': '654321',
        'max_connections': 1000,
        'decode_responses': True,
    }
