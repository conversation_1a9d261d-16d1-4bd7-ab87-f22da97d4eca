import os
from pathlib import Path, PurePath

DEV = 'DEV'
TEST = 'TEST'
PROD = 'PROD'


class BaseConfig:
    # 环境配置
    SERVER_ENV = ''
    SECRET_KEY = 'MGQ0MjY3NzNhMmY0'
    AES_SECRET_KEY = 'aigc_aes_secret_key'
    FET_AES_SECRET_KEY = b'fet aes secret key'

    # 项目配置
    DEBUG = False
    TITLE = 'hw-2505'
    VERSION = '0.0.1'
    DESCRIPTION = 'hw-2505'
    DOCS_URL = None
    REDOCS_URL = None
    OPENAPI_URL = None

    # 认证版本
    AUTH_VERSION = 'v1'
    # 自动设置路遇
    AUTO_SET_ROUTE = False

    ROOT_PATH = Path(__file__).absolute().parent.parent.parent
    DOWNLOAD_DIR = PurePath.joinpath(ROOT_PATH, 'downloads')

    # 雪花算法配置信息
    SNOWFLAKE_INFO = {
        'datacenter_id': int(os.getenv('SNOWFLAKE_DATACENTER_ID', 1)),
        'worker_id': int(os.getenv('SNOWFLAKE_WORKER_ID', 1)),
    }

    # alioss
    # ALI_OSS_AUTH = {
    #     'access_key': 'LTAI5tAVLtra5poigCSsugpG',
    #     'access_key_secret': '******************************',
    #     'role_arn': 'acs:ram::1062142063296397:role/role-arn-aigc-api-center',
    #     'Action': ['oss:PutObject', 'oss:oss:GetObject'],
    #     'bucket_name': 'kavin-photos',
    # }

    # 'access_key': 'LTAI5tGyv6cCyZ2PoQYuTPyk',
    # 'access_key_secret': '******************************',


    # 日志配置
    LOG_CONFIG = {
        'LOG_DIR': '/root/log',
        'APP_LOGGER': {
            'ALI_LOG_PROJECT': '',
            'ALI_LOG_STORE': '',
            'ALI_LOG_ERROR_STORE': '',
            'ALI_LOG_ENDPOINT': 'cn-hangzhou.log.aliyuncs.com',
            'ALI_LOG_ACCESS_KEY_ID': '',
            'ALI_LOG_ACCESS_KEY_SECRET': '',
        },
    }

    # DB
    DB_CONFIG = {
        'user': 'root',
        'host': '127.0.0.1',
        'password': '654321',
        'port': 3306,
        'database': 'test',
    }

    # REDIS
    REDIS_CONFIG = {
        'host': 'localhost',
        'port': 6379,
        'username': 'default',
        'password': '654321',
        'max_connections': 200,
        'decode_responses': True,
    }
    REDIS_URL = f"redis://:{REDIS_CONFIG['password']}@{REDIS_CONFIG['host']}:6379"

    # 过滤的urls
    EXCLUDE_URLS = [
        '/docs',
        '/openapi.json',
        '/redoc',
        '/docs/oauth2-redirect',
        '/web/setting/urls',
        '/web/ai/login',
        '/web/ai/task',
        '/web/ai/task/upload',
    ]


    @classmethod
    def is_prod(cls):
        return cls.SERVER_ENV == PROD

    @classmethod
    def is_test(cls):
        return cls.SERVER_ENV == TEST

    @classmethod
    def is_dev(cls):
        return cls.SERVER_ENV == DEV

    @classmethod
    def get_download_path(cls):
        if not os.path.exists(cls.DOWNLOAD_DIR):
            os.makedirs(cls.DOWNLOAD_DIR)
        return cls.DOWNLOAD_DIR
