from pathlib import Path, PurePath

from .base import DEV, BaseConfig


class DevConfig(BaseConfig):
    SERVER_ENV = DEV
    DEBUG = True

    DOCS_URL = '/docs'
    OPENAPI_URL = '/openapi.json'

    AUTO_SET_ROUTE = True

    # 日志配置
    LOG_CONFIG = {
        'LOG_DIR': PurePath.joinpath(Path(__file__).absolute().parent.parent.parent, 'log'),
        'APP_LOGGER': {
            'ALI_LOG_PROJECT': '',
            'ALI_LOG_STORE': '',
            'ALI_LOG_ERROR_STORE': '',
            'ALI_LOG_ENDPOINT': 'cn-hangzhou.log.aliyuncs.com',
            'ALI_LOG_ACCESS_KEY_ID': '',
            'ALI_LOG_ACCESS_KEY_SECRET': '',
        },
        'CELERY_APP_LOGGER': {
            'ALI_LOG_PROJECT': '',
            'ALI_LOG_STORE': '',
            'ALI_LOG_ERROR_STORE': '',
            'ALI_LOG_ENDPOINT': 'cn-hangzhou.log.aliyuncs.com',
            'ALI_LOG_ACCESS_KEY_ID': '',
            'ALI_LOG_ACCESS_KEY_SECRET': '',
        },
    }

    # DB
    DB_CONFIG = {
        'user': 'root',
        'host': '127.0.0.1',
        'password': '654321',
        'port': 3306,
        'database': 'hw-2505',
    }

    # REDIS
    REDIS_CONFIG = {
        'host': 'localhost',
        'port': 6379,
        'username': 'default',
        'password': '654321',
        'max_connections': 200,
        'decode_responses': True,
    }

