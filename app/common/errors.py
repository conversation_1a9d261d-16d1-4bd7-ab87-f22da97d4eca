class BaseError(Exception):
    """ 异常基础类"""

    def __init__(self, code: int = 500, message: str = '', **kwargs):
        self.code = code
        self.message = message
        self.data = kwargs

# 定义一个辅助函数来创建异常类


def create_error_class(class_name, default_code, docstring):
    class CustomError(BaseError):
        __doc__ = docstring

        def __init__(self, code: int = default_code, message: str = '', **kwargs):
            super().__init__(code, message, **kwargs)
    CustomError.__name__ = class_name
    return CustomError


# 使用辅助函数创建具体的异常类
ClientError = create_error_class('ClientError', 400, ' 客户端请求错误')
ServerError = create_error_class('ServerError', 500, ' 服务错误')
BizError = create_error_class('BizError', 500, ' 业务错误')
AuthError = create_error_class('AuthError', 401, ' 认证错误')
