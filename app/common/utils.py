# flake8: noqa
# noqa: E501
import hashlib
import random
import socket
import time
import uuid


LETTERS = 'abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ'
DIGITS = '0123456789'


def to_bytes(text, fmt='utf8'):
    """ 转成bytes"""
    if isinstance(text, str):
        text = text.encode(fmt)
    return text


def to_string(text, fmt='utf8'):
    """ 转成string"""
    if isinstance(text, bytes):
        text = text.decode(fmt, 'ignore')
    return text


def make_sha1_hash(text):
    text = to_bytes(text)
    sha1_hash = hashlib.sha1(text).hexdigest()
    return sha1_hash


def make_sms_code(length=6):
    return ''.join([random.choice(DIGITS) for i in range(length)])


def make_rand_text(length=8, _type=None):
    if _type is None:
        return ''.join([random.choice(LETTERS + DIGITS) for i in range(length)])
    elif _type.lower() == 'number':
        return ''.join([random.choice(DIGITS) for i in range(length)])
    elif _type.lower() == 'string':
        return ''.join([random.choice(LETTERS) for i in range(length)])


def make_uuid_path():
    text = str(int(time.time()))[::-1]
    uuid = hashlib.md5(to_bytes(text)).hexdigest()[:16]
    return '-'.join([uuid[i:i + 4] for i in range(0, len(uuid), 4)])


def make_rand_uuid():
    uuid = make_rand_text(length=32)
    return uuid.lower()


def make_out_trade_no():
    a = str(time.strftime('%Y%m%d%H%M%S', time.localtime(time.time())))
    b = str(time.time()).replace('.', '')[-7:]
    return a + b


def make_uuid():
    return uuid.uuid1().hex


def make_uuid4():
    return uuid.uuid4().hex


def get_host_ip():
    try:
        s = socket.socket(socket.AF_INET, socket.SOCK_DGRAM)
        s.connect(('*******', 80))
        ip = s.getsockname()[0]
    finally:
        s.close()
    return ip
