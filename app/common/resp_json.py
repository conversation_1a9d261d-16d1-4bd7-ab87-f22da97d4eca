from fastapi.responses import JSONResponse


class ApiJsonify:

    @classmethod
    def response(cls, code, message='', **kw):
        status_code = 500 if code > 500 else code
        return JSONResponse(
            status_code=status_code,
            content=dict(
                code=code,
                message=message,
                data=kw,
            ),
        )

    @classmethod
    def ok(cls, code=200, message='', **kw):
        return cls.response(code=code, message=message, **kw)

    @classmethod
    def no(cls, message, code=500, **kw):
        return cls.response(code=code, message=message, **kw)
