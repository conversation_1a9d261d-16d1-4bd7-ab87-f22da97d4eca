import time

from fastapi import Header
from fastapi import Query

from app.common.errors import AuthError


class TokenAuth:

    @staticmethod
    async def admin_auth(token: str = Header()):
        if token != 'e62899bc225e4de482486fbfea9da6af':
            raise AuthError(message='无效的TOKEN')
        return token

    @staticmethod
    async def client_auth(token: str = Header()):
        if token != '5ea50121dbca40e8b3751741a36ea6d7':
            raise AuthError(message='无效的TOKEN')
        return token
