import os
from pathlib import Path
from pathlib import PurePath

from loguru import logger as _logger

from app.config.settings import Settings

APP_LOGGER = 'APP_LOGGER'


class Logger:

    def __init__(self, logger_name=APP_LOGGER, prefix='server'):
        self.logger_name = logger_name
        self.prefix = prefix
        self.APP_LOG_DIR = Settings.LOG_CONFIG['LOG_DIR']
        self.sls_config = Settings.LOG_CONFIG[logger_name]
        self._create_log_directory()

    def _create_log_directory(self):
        """创建日志目录"""
        try:
            if not Path.exists(self.APP_LOG_DIR):
                os.makedirs(self.APP_LOG_DIR)
        except OSError as e:
            _logger.error(f"Failed to create log directory {self.APP_LOG_DIR}: {e}")

    def get_logger(self):
        """ 获取日志"""
        info_file = PurePath.joinpath(self.APP_LOG_DIR, self.prefix + '-info-{time:YYYY-MM-DD}.log')
        error_file = PurePath.joinpath(self.APP_LOG_DIR, self.prefix + '-error-{time:YYYY-MM-DD}.log')

        app_logger = _logger.bind(logger_name=self.logger_name)
        self._add_log_handler(app_logger, info_file, 'INFO')
        self._add_log_handler(app_logger, error_file, 'ERROR')
        return app_logger

    def _get_filter(self, level):
        """获取日志过滤器"""
        return lambda record: record['level'].name == level and record['extra']['logger_name'] == self.logger_name

    def _add_log_handler(self, logger, file_path, level):
        """添加日志处理程序"""
        logger.add(
            file_path,
            level=level,
            rotation='0:00',
            filter=self._get_filter(level),
            backtrace=level == 'ERROR',
            diagnose=level == 'ERROR',
            enqueue=True,
        )


logger = Logger(APP_LOGGER, 'server').get_logger()
