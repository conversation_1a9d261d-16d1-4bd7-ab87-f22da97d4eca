import time
from datetime import datetime
from pprint import pprint
from typing import Any
from typing import Dict
from typing import Tuple

from fastapi import APIRouter
from fastapi import Request
from fastapi import Response
from fastapi.routing import APIRoute
from starlette.responses import StreamingResponse

from app.common.logger import logger
from app.common.utils import make_uuid
from app.common.utils import to_string
from app.config.settings import Settings


class RouteError(Exception):
    """路由相关异常基类"""
    pass


class InvalidRequestError(RouteError):
    """无效请求异常"""
    pass


def get_current_time() -> int:
    """获取当前时间戳（毫秒）"""
    return int(time.time() * 1000)


def get_request_ip(request: Request) -> str:
    """
    获取请求的 IP 地址。
    优先从 X-Forwarded-For 头中获取，如果没有则从 host 头中获取。
    """
    forwarded_for = request.headers.get('X-Forwarded-For')
    if forwarded_for:
        return forwarded_for.split(',')[0].strip()
    return request.headers.get('host', '')


async def set_body(request: Request) -> None:
    """
    设置请求体，用于后续读取。
    """
    receive_ = await request._receive()

    async def receive():
        return receive_
    request._receive = receive


async def get_request_body(request: Request) -> Tuple[str, Dict[str, Any]]:
    """
    获取请求体数据，尝试解析为 JSON 格式。
    """
    body_data = ''
    json_data = {}
    if request.method.upper() != 'GET':
        try:
            body_data = to_string(await request.body())
        except Exception as e:
            logger.debug(f'parse_request_body_error: {str(e)}')
        try:
            json_data = await request.json()
        except ValueError as e:
            logger.debug(f'parse_request_json_error: {str(e)}')
    return body_data, json_data


async def get_response_body(response: Response) -> Tuple[str, Response]:
    """
    获取响应体数据。
    如果是流式响应，将流式数据合并为一个整体。
    """
    resp_body = b''
    if isinstance(response, StreamingResponse):
        res_body = b''
        async for item in response.body_iterator:
            res_body += item
        response = Response(
            content=res_body,
            status_code=response.status_code,
            headers=dict(response.headers),
            media_type=response.media_type,
        )
        resp_body = res_body
    else:
        resp_body = response.body
    return to_string(resp_body), response


class LogMixin:
    def get_route_handler(self):
        original_route_handler = super().get_route_handler()

        async def custom_route_handler(request: Request) -> Response:
            start_time = get_current_time()
            start_time_str = str(datetime.now())

            try:
                response: Response = await original_route_handler(request)
            except Exception as e:
                logger.debug(f"Request processing failed: {str(e)}")
                raise

            body_data, json_data = await get_request_body(request)
            resp_body, response = await get_response_body(response)

            latency_time = get_current_time() - start_time
            request_data = {
                'request_uid': make_uuid(),
                'request_start_time': start_time,
                'request_start_time_str': start_time_str,
                'request_ip': get_request_ip(request),
                'url': str(request.url),
                'method': request.method,
                'headers': str(request.headers),
                'query': str(request.query_params),
                'body': body_data,
                'json': json_data,
                'resp': resp_body,
                'latency': latency_time,
                'latency_time': f'{latency_time}ms',
            }

            if Settings.is_dev():
                pprint(request_data)
            else:
                logger.info(request_data)
            return response

        return custom_route_handler


class RestRoute(LogMixin, APIRoute):
    pass


class RestRouter(APIRouter):
    def __init__(self, **kwargs):
        super().__init__(route_class=RestRoute, **kwargs)
        self._default_router = None
        self.kwargs = kwargs

    @property
    def default_router(self) -> APIRouter:
        if self._default_router is None:
            self._default_router = APIRouter(**self.kwargs)
        return self._default_router


def create_router(**kwargs) -> RestRouter:
    return RestRouter(**kwargs)
