import asyncio
import sys
from contextlib import asynccontextmanager

from redis.asyncio.client import Redis
from redis.asyncio.connection import ConnectionPool
from redis.exceptions import AuthenticationError, TimeoutError

from app.common.logger import logger
from app.common.utils import make_uuid4
from app.config.settings import Settings


class RedisManger:

    @classmethod
    def get_client(cls):
        return Redis(**Settings.REDIS_CONFIG).client()

    @classmethod
    async def set(cls, key, value, ex=None, px=None, nx=False, xx=False):
        """
        设置值
        :param key:
        :param value:
        :param ex: 过期时间（秒）
        :param px: 过期时间（毫秒）
        :param nx: 如果设置为True，则只有name不存在时，当前set操作才执行
        :param xx: 如果设置为True，则只有name存在时，当前set操作才执行
        :return:
        """
        async with cls.get_client() as conn:
            await conn.ping()
            await conn.set(key, value, ex=ex, px=px, nx=nx, xx=xx)

    @classmethod
    async def get(cls, key):
        """
        获取值
        :param key:
        :return:
        """
        async with cls.get_client() as conn:
            await conn.ping()
            return await conn.get(key)

    @classmethod
    async def delete(cls, key):
        """
        删除值
        :param key:
        :return:
        """
        async with cls.get_client() as conn:
            await conn.ping()
            return await conn.delete(key)

    @classmethod
    async def expire(cls, key, time):
        """
        设置过期时间
        :param key:
        :param time: 过期时间（秒）
        :return:
        """
        async with cls.get_client() as conn:
            await conn.ping()
            return await conn.expire(key, time)

    @classmethod
    async def exists(cls, key):
        """
        判断key是否存在
        :param key:
        :return:
        """
        async with cls.get_client() as conn:
            await conn.ping()
            return await conn.exists(key)

    @classmethod
    async def mget(cls, keys):
        """
        批量获取
        :param keys:
        :return:
        """
        async with cls.get_client() as conn:
            await conn.ping()
            return await conn.mget(keys)

    @classmethod
    async def mset(cls, key_value_dict):
        """
        批量设置
        :param key_value_dict:
        :return:
        """
        async with cls.get_client() as conn:
            await conn.ping()
            return await conn.mset(key_value_dict)

    @classmethod
    async def pipeline(cls):
        """
        批量操作
        :return:
        """
        async with cls.get_client().pipeline() as conn:
            await conn.ping()
            return await conn.pipeline()


class RedisProdManager(RedisManger):

    @classmethod
    def get_client(cls):
        """
        获取redis连接
        :return:
        """
        return Redis.from_url(Settings.PROD_REDIS_URL)


redis_manager = RedisManger()
redis_prod_manager = RedisProdManager()


class RedisStore(Redis):
    """ redis 连接池"""

    def __init__(self):
        pool = ConnectionPool(**Settings.REDIS_CONFIG)
        super().__init__(connection_pool=pool)

    async def open(self):
        try:
            await self.ping()
        except TimeoutError:
            logger.error('❌ 数据库 redis 连接超时')
            sys.exit()
        except AuthenticationError:
            logger.error('❌ 数据库 redis 连接认证失败')
            sys.exit()
        except Exception as e:
            logger.error(f'❌ 数据库 redis 连接异常 {str(e)}')
            sys.exit()


redis_store = RedisStore()


class RedisLock:
    """ 分布式锁"""

    def __init__(self, store, lock_key, timeout=3, wait_interval=0.2, expires=3):
        self.store = store
        self.lock_key = lock_key
        self.lock_value = make_uuid4()
        self.timeout = timeout  # 尝试获取锁的时间
        self.wait_interval = wait_interval  # 尝试获取锁的等待间隔
        self.expires = expires  # 一个锁自然过期的时长

    @asynccontextmanager
    async def acquire(self):
        """ 尝试获取锁"""
        timeout = self.timeout
        acquired = False
        while timeout > 0 and not acquired:
            lock = await self.store.set(self.lock_key, self.lock_value, nx=True, ex=self.expires)
            if lock:
                acquired = True
            else:
                await asyncio.sleep(self.wait_interval)
                timeout -= self.wait_interval

        if not acquired:
            raise asyncio.TimeoutError('Failed to acquire lock within the specified time.')

        try:
            yield
        finally:
            if acquired:
                await self.release()

    async def release(self):
        """ 释放锁"""
        while True:
            try:
                # 使用Lua脚本保证操作的原子性，避免误删其他客户端的锁
                lua_script = """
                if redis.call("get", KEYS[1]) == ARGV[1] then
                    return redis.call("del", KEYS[1])
                else
                    return 0
                end
                """
                _lock = self.store.register_script(lua_script)
                result = await _lock(keys=[self.lock_key], args=[self.lock_value])
                if result == 1:
                    logger.debug(f'Release Lock: {self.lock_key} {self.lock_value}')
                    break  # 锁成功删除
            except Exception as e:
                # 如果Redis服务器不可用，重试
                logger.error(f'redis_lock_release_error => key: {self.lock_key}, \
                    value: {self.lock_value}, errmsg: {str(e)}')
                await asyncio.sleep(self.wait_interval)
