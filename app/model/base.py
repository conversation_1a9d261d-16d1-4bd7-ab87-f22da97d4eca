import datetime
import logging
from typing import Any
from typing import Optional
from typing import Type
from typing import Union

import pytz
from tortoise import fields
from tortoise import Model
from tortoise import Tortoise
from tortoise.manager import Manager
from app.common.redis_store import redis_store
from app.common.utils import to_bytes

from app.config.settings import Settings
import orjson as json


def get_config():
    mysql_config = {
        'connections': {
            # Dict format for connection
            'default': {
                'engine': 'tortoise.backends.mysql',
                'credentials': {
                    'host': Settings.DB_CONFIG['host'],
                    'port': Settings.DB_CONFIG['port'],
                    'user': Settings.DB_CONFIG['user'],
                    'password': Settings.DB_CONFIG['password'],
                    'database': Settings.DB_CONFIG['database'],
                    'echo': True,
                    'pool_recycle': 3600,
                },
            },
        },
        'apps': {
            'models': {
                'models': ['app.model'],
                'default_connection': 'default',
            },
        },
        'use_tz': False,
        'timezone': 'Asia/Shanghai',
    }
    return mysql_config


async def init_mysql_config():
    """ 初始化数据库配置"""

    if Settings.is_prod():
        logger = logging.getLogger('tortoise')
        logger.setLevel(logging.ERROR)
    await Tortoise.init(config=get_config())


class SoftDelManger(Manager):
    def get_queryset(self):
        return super().get_queryset().filter(deleted=0)


def to_naive(value: datetime.datetime) -> datetime.datetime:

    if value.tzinfo is None:
        return value

    value = value.astimezone(pytz.timezone('Asia/Shanghai'))
    return value


class NativeDatetimeField(fields.DatetimeField):
    skip_to_python_if_native = True

    class _db_postgres:  # noqa
        SQL_TYPE = 'TIMESTAMP'

    def to_python_value(self, value: Any) -> Optional[datetime.datetime]:
        value = super().to_python_value(value)
        if value is None:
            return value

        return to_naive(value)

    def to_db_value(
        self,
        value: Optional[datetime.datetime],
        instance: 'Union[Type[Model], Model]',
    ) -> Optional[datetime.datetime]:
        value = super().to_db_value(value, instance)
        if value is None:
            return value

        return to_naive(value)


class Base(Model):

    id = fields.IntField(pk=True, index=True, auto_generate=True)
    create_time = NativeDatetimeField(auto_now_add=True)
    update_time = NativeDatetimeField(auto_now=True)
    delete_time = NativeDatetimeField(auto_now_add=True)
    deleted = fields.BooleanField(default=False)

    def __str__(self):
        return f'<{self.__class__.__name__} id: {self.id}>'

    class Meta:
        table = ''
        abstract = True


class CacheModel(Base):
    __abstract__ = True

    # 缓存相关的属性
    cache_key = 'id'
    cache_expiration = 3600  # 缓存过期时间（秒）
    redis_db = redis_store

    def __init__(self, *args, **kwargs):
        super().__init__(*args, **kwargs)

    @classmethod
    def get_cache_key(cls, cache_key: int | str, suffix: str = None) -> str:
        """生成缓存键"""
        if suffix:
            return f"{cls.Meta.table}:{cache_key}{suffix}"
        return f"{cls.Meta.table}:{cache_key}"

    @classmethod
    async def get_from_cache(cls, cache_key: int | str, suffix: str = None) -> Optional[Any]:
        key = cls.get_cache_key(cache_key, suffix)
        # cached_data = await cls.redis_db.get(key)  # 异步获取数据
        cached_data = await cls.redis_db.hgetall(key)  # 异步获取数据
        if cached_data:
            ensure_data = cls.load_ensure_dict2(cached_data)
            return cls.from_dict(ensure_data)
        return None

    @classmethod
    async def set_in_cache(cls, obj: Any, suffix: str = None) -> None:
        if not isinstance(obj, cls):
            raise TypeError(f"Expected instance of {cls.__name__}, got {type(obj)}")
        cache_key = getattr(obj, cls.Meta.cache_key) or getattr(obj, cls.cache_key) or getattr(obj, 'id')
        if not cache_key:
            raise ValueError("Cache key is required")
        key = cls.get_cache_key(cache_key, suffix)

        serialized_data = cls.ensure_dict(obj)
        # await cls.redis_db.setex(key, cls.cache_expiration, serialized_data)  # 异步设置带过期时间的数据
        print("searialized_data", serialized_data, obj.to_dict())
        await cls.redis_db.hmset(key, serialized_data)
        # await cls.redis_db.set(key, serialized_data)

    async def save_to_cache(self, suffix=None):
        """保存当前对象到缓存"""
        await self.set_in_cache(self, suffix)

    async def invalidate_cache(self, suffix=None):
        """从缓存中删除当前对象"""
        cache_key = getattr(self, self.cache_key) or self.id
        key = self.get_cache_key(cache_key, suffix)
        await self.redis_db.delete(key)  # 异步删除键

    def to_dict(self) -> dict:
        """将模型转换为字典"""
        cache_fields = getattr(self.Meta, 'cache_fields', [])
        if cache_fields:
            return {field: getattr(self, field) for field in cache_fields}
        else:
            return {field: getattr(self, field) for field in self._meta.db_fields}
    
    @classmethod
    def from_dict(cls, data: dict) -> Any:
        """从字典创建模型实例"""
        instance = cls(**data)
        return instance
    
    @classmethod
    def ensure_dict(cls, obj: Any):
        obj_dict = obj.to_dict()
        data = {}
        for key, value in obj_dict.items():
            if isinstance(value, bool):
                data[key] = 'true' if value else 'false'
            else:
                data[key] = value
            # if isinstance(value, (datetime.datetime, )):
            #     data[key] = value.strftime('%Y-%m-%d %H:%M:%S')
            # else:
            #     data[key] = value
        return data
        # return json.dumps(data)

    clas
    
    @classmethod
    def load_ensure_dict(cls, obj_json_data: str):
        data = {}
        obj_json_data = to_bytes(obj_json_data)
        if not obj_json_data:
            return {}
        obj_dict = json.loads(obj_json_data)
        for key, value in obj_dict.items():
            if isinstance(value, (str, )) and len(value) == 19:
                try:
                    v = datetime.datetime.strptime(value, '%Y-%m-%d %H:%M:%S')
                    data[key] = v
                except:
                    continue
            data[key] = value
        return data
    
    @classmethod
    def load_ensure_dict2(cls, obj_data: dict):
        data = {}
        for key, value in obj_data.items():
            if value in ['true', 'false']:
                data[key] = True if value == 'true' else False
            else:
                data[key] = value
        return data

def datetime_serializer(obj):
    if isinstance(obj, (datetime.datetime,)):
        return obj.isoformat()
    

def datetime_unserializer(json_dict):
    if '__datetime__' in json_dict:
        return datetime.fromisoformat(json_dict['__datetime__'])
    return {k: datetime_unserializer(v) if isinstance(v, dict) else v for k, v in json_dict.items()}
