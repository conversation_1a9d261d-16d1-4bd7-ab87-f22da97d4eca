from datetime import datetime
from typing import Any, Dict, Optional

# 第三方库导入
import orjson as json
from tortoise import Model, fields


# 本地应用导入
from .base import Base, SoftDelManger, CacheModel


class Meeting(CacheModel):
    """会议"""
    version = fields.CharField(max_length=20, default='0.0.1') # 版本
    fayan = fields.BooleanField(default=False) # 发言状态
    username_limit = fields.IntField(default=0) # 名称数量
    memo = fields.BooleanField(default=False) # 笔记状态
    screen = fields.BooleanField(default=False) # 直播画面切换状态

    class Meta:
        table = 'meeting'
        manager = SoftDelManger()
        cache_key = 'id'
        cache_fields = ['version', 'fayan', 'username_limit', 'memo', 'screen']


class Toupiao(CacheModel):
    """ 投票"""

    status = fields.BooleanField(default=True)
    duoxuan = fields.BooleanField(default=True)
    title1 = fields.Char<PERSON>ield(max_length=255, default='')
    title2 = fields.CharField(max_length=255, default='')
    option1_list = fields.TextField(default='')
    option2_list = fields.TextField(default='')

    class Meta:
        table = 'toupiao'
        manager = SoftDelManger()
        cache_key = 'id'

    def json(self):
        return {
            'status': self.status,
            'duoxuan': self.duoxuan,
            'title1': self.title1,
            'title2': self.title2,
            'option1_list': json.loads(self.option1_list),
            'option2_list': json.loads(self.option2_list),
        }


class Option(Base):
    """ 投票记录"""
    option1 = fields.TextField()
    option2 = fields.TextField()

    class Meta:
        table = 'toupiao_record'
        manager = SoftDelManger()

    def json(self):
        return {
            'option1': self.option1,
            'option2': self.option2,
        }


class User(Base):
    """ 用户"""
    name = fields.CharField(max_length=20, default='')

    class Meta:
        table = 'user'
        manager = SoftDelManger()


class Username(Base):
    """ 用户名"""

    name = fields.CharField(max_length=20, default='')

    class Meta:
        table = 'username'
        manager = SoftDelManger()


class Image(CacheModel):
    """ 图片数据"""
    
    type = fields.CharField(max_length=20, default='')
    url_list = fields.TextField()
    need_update = fields.BooleanField(default=False)

    class Meta:
        table = 'image'
        manager = SoftDelManger()
        cache_key = 'type'


class Email(Base):
    """ 邮件列表"""

    email = fields.CharField(max_length=255, default='')
    img_list = fields.TextField()
    status = fields.BooleanField(default=False)

    class Meta:
        table = 'email'
        manager = SoftDelManger()

    class Meta:
        table = 'email'
        manager = SoftDelManger()
