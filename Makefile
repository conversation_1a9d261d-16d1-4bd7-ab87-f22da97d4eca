# 定义镜像名称和容器名称
IMAGE_NAME := registry.cn-hangzhou.aliyuncs.com/photobooth/hw-2505:latest
CONTAINER_NAME := hw-service
NAME := hw2505


dev:
	gunicorn -c unicorn.py main:app

# 下载镜像的规则
pull:
	docker pull $(IMAGE_NAME)

# 运行容器的规则
run: pull
	docker run -e SERVER_ENV="test" \
        -v /root/log:/${NAME}/log \
		--network deploy_root_network \
        --name $(CONTAINER_NAME) \
        -p 7200:5000 -d $(IMAGE_NAME) gunicorn -c unicorn.py main:app

# 停止并删除容器的规则
clean:
	docker stop $(CONTAINER_NAME) || true
	docker rm $(CONTAINER_NAME) || true

# 显示帮助信息的规则
help:
	@echo "使用方法:"
	@echo "  make pull     - 下载镜像"
	@echo "  make run      - 下载镜像并运行容器"
	@echo "  make clean    - 停止并删除容器"
	@echo "  make help     - 显示此帮助信息"

.PHONY: pull run clean help
