import os
log_dir = './log'
if not os.path.exists(log_dir):
    os.makedirs(log_dir)

bind = '0.0.0.0:5000'
workers = 8
# worker_class = 'gunicorn.workers.ggevent.GeventWorker'
worker_class = 'uvicorn.workers.UvicornWorker'
threads = 1
x_forwarded_for_header = 'X-FORWARDED-FOR'
pidfile = 'log/gunicorn.pid'
accesslog = 'log/access.log'
errorlog = 'log/gunicorn.log'
loglevel = 'info'
max_requests = 5000
max_requests_jitter = 500
timeout = 60
